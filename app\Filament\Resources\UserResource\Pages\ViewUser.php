<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->disabled(),

                Forms\Components\TextInput::make('email')
                    ->disabled(),

                Forms\Components\TextInput::make('created_at')
                    ->label('Joined')
                    ->disabled()
                    ->formatStateUsing(fn($state) => $state?->format('M j, Y H:i')),

                Forms\Components\TextInput::make('updated_at')
                    ->label('Last Updated')
                    ->disabled()
                    ->formatStateUsing(fn($state) => $state?->format('M j, Y H:i')),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
