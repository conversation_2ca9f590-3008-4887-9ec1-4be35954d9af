# WMS MPI

## Setup

Add env
```env
APP_URL=https://wms-mpi.test/

APP_LOCALE=id
APP_FALLBACK_LOCALE=id
APP_FAKER_LOCALE=id_ID
APP_TIMEZONE=Asia/Jakarta
```

Generate key
```sh
php artisan key:generate
```

Migrate DB
```sh
php artisan migrate
```

Add admin user to filament
```sh
php artisan make:filament-user
```

Run the app
```sh
composer run dev
```

## Recommendation
Use tools like DBNgin for Database MySQL etc. and Herd for easier dev environment (Nginx, php, nodejs)