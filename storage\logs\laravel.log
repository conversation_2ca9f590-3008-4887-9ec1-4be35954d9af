[2025-06-02 12:08:52] local.ERROR: Class "App\Filament\Resources\UserResource\Pages\ViewUser" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\UserResource\\Pages\\ViewUser\" not found at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource.php:122)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\UserResource::getPages()
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::{closure:Filament\\Facades\\Filament::registerPanel():140}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->{closure:Filament\\FilamentServiceProvider::packageRegistered():47}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php:12}(Object(Illuminate\\Routing\\Router))
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\Users\\\\<USER>\\\\He...')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\He...')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 {main}
"} 
[2025-06-02 12:08:52] local.ERROR: Class "App\Filament\Resources\UserResource\Pages\ViewUser" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\UserResource\\Pages\\ViewUser\" not found at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource.php:122)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\UserResource::getPages()
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::{closure:Filament\\Facades\\Filament::registerPanel():140}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->{closure:Filament\\FilamentServiceProvider::packageRegistered():47}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php:12}(Object(Illuminate\\Routing\\Router))
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\Users\\\\<USER>\\\\He...')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\He...')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 {main}
"} 
[2025-06-02 12:09:22] local.ERROR: Class "App\Filament\Resources\UserResource\Pages\ViewUser" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\UserResource\\Pages\\ViewUser\" not found at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource.php:122)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\UserResource::getPages()
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::{closure:Filament\\Facades\\Filament::registerPanel():140}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->{closure:Filament\\FilamentServiceProvider::packageRegistered():47}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php:12}(Object(Illuminate\\Routing\\Router))
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\Users\\\\<USER>\\\\He...')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\He...')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Herd\\wms-mpi\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}
"} 
[2025-06-02 12:10:14] local.ERROR: Command "filament:list-resources" is not defined.

Did you mean one of these?
    filament:about
    filament:assets
    filament:cache-components
    filament:check-translations
    filament:clear-cached-components
    filament:install
    filament:optimize
    filament:optimize-clear
    filament:upgrade {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"filament:list-resources\" is not defined.

Did you mean one of these?
    filament:about
    filament:assets
    filament:cache-components
    filament:check-translations
    filament:clear-cached-components
    filament:install
    filament:optimize
    filament:optimize-clear
    filament:upgrade at C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('filament:list-r...')
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
