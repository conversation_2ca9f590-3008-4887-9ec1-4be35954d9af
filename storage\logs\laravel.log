[2025-06-02 12:08:52] local.ERROR: Class "App\Filament\Resources\UserResource\Pages\ViewUser" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\UserResource\\Pages\\ViewUser\" not found at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource.php:122)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\UserResource::getPages()
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::{closure:Filament\\Facades\\Filament::registerPanel():140}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->{closure:Filament\\FilamentServiceProvider::packageRegistered():47}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php:12}(Object(Illuminate\\Routing\\Router))
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\Users\\\\<USER>\\\\He...')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\He...')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 {main}
"} 
[2025-06-02 12:08:52] local.ERROR: Class "App\Filament\Resources\UserResource\Pages\ViewUser" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\UserResource\\Pages\\ViewUser\" not found at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource.php:122)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\UserResource::getPages()
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::{closure:Filament\\Facades\\Filament::registerPanel():140}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->{closure:Filament\\FilamentServiceProvider::packageRegistered():47}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php:12}(Object(Illuminate\\Routing\\Router))
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\Users\\\\<USER>\\\\He...')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\He...')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 {main}
"} 
[2025-06-02 12:09:22] local.ERROR: Class "App\Filament\Resources\UserResource\Pages\ViewUser" not found {"exception":"[object] (Error(code: 0): Class \"App\\Filament\\Resources\\UserResource\\Pages\\ViewUser\" not found at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource.php:122)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(525): App\\Filament\\Resources\\UserResource::getPages()
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::{closure:Filament\\Facades\\Filament::registerPanel():140}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->{closure:Filament\\FilamentServiceProvider::packageRegistered():47}(Object(Illuminate\\Foundation\\Application), Array)
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('filament')
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure:C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php:12}(Object(Illuminate\\Routing\\Router))
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('C:\\\\Users\\\\<USER>\\\\He...')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Users\\\\<USER>\\\\He...')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#37 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Herd\\wms-mpi\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}
"} 
[2025-06-02 12:10:14] local.ERROR: Command "filament:list-resources" is not defined.

Did you mean one of these?
    filament:about
    filament:assets
    filament:cache-components
    filament:check-translations
    filament:clear-cached-components
    filament:install
    filament:optimize
    filament:optimize-clear
    filament:upgrade {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"filament:list-resources\" is not defined.

Did you mean one of these?
    filament:about
    filament:assets
    filament:cache-components
    filament:check-translations
    filament:clear-cached-components
    filament:install
    filament:optimize
    filament:optimize-clear
    filament:upgrade at C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('filament:list-r...')
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-06-02 12:13:52] local.ERROR: Call to a member function format() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function format() on string at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource\\Pages\\ViewUser.php:28)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\UserResource\\Pages\\ViewUser->{closure:App\\Filament\\Resources\\UserResource\\Pages\\ViewUser::form():28}('2025-06-01T06:3...')
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php(149): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Component->{closure:Filament\\Forms\\Components\\Concerns\\HasState::formatStateUsing():149}(Object(Filament\\Forms\\Components\\TextInput))
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php(86): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php(223): Filament\\Forms\\Components\\Component->callAfterStateHydrated()
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Concerns\\HasState.php(203): Filament\\Forms\\Components\\Component->hydrateState(NULL, true)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Concerns\\HasState.php(188): Filament\\Forms\\ComponentContainer->hydrateState(NULL, true)
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php(98): Filament\\Forms\\ComponentContainer->fill(Array)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php(81): Filament\\Resources\\Pages\\ViewRecord->fillFormWithDataAndCallHooks(Object(App\\Models\\User))
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php(64): Filament\\Resources\\Pages\\ViewRecord->fillForm()
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Resources\\Pages\\ViewRecord->mount('1')
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), Array, NULL, false)
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), Array, NULL, false)
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), Array, NULL, false)
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('1')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), '__invoke')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#48 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#55 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#59 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#84 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#85 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#86 C:\\Users\\<USER>\\Herd\\wms-mpi\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#87 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\He...')
#88 {main}
"} 
[2025-06-02 12:15:52] local.ERROR: Call to a member function format() on string {"userId":1,"exception":"[object] (Error(code: 0): Call to a member function format() on string at C:\\Users\\<USER>\\Herd\\wms-mpi\\app\\Filament\\Resources\\UserResource\\Pages\\ViewUser.php:28)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): App\\Filament\\Resources\\UserResource\\Pages\\ViewUser->{closure:App\\Filament\\Resources\\UserResource\\Pages\\ViewUser::form():28}('2025-06-01T06:3...')
#1 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php(149): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#2 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php(35): Filament\\Forms\\Components\\Component->{closure:Filament\\Forms\\Components\\Concerns\\HasState::formatStateUsing():149}(Object(Filament\\Forms\\Components\\TextInput))
#3 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php(86): Filament\\Support\\Components\\Component->evaluate(Object(Closure))
#4 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php(223): Filament\\Forms\\Components\\Component->callAfterStateHydrated()
#5 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Concerns\\HasState.php(203): Filament\\Forms\\Components\\Component->hydrateState(NULL, true)
#6 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\forms\\src\\Concerns\\HasState.php(188): Filament\\Forms\\ComponentContainer->hydrateState(NULL, true)
#7 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php(98): Filament\\Forms\\ComponentContainer->fill(Array)
#8 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php(81): Filament\\Resources\\Pages\\ViewRecord->fillFormWithDataAndCallHooks(Object(App\\Models\\User))
#9 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php(64): Filament\\Resources\\Pages\\ViewRecord->fillForm()
#10 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Resources\\Pages\\ViewRecord->mount('1')
#11 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#12 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#15 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#16 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#17 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#18 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#19 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), Array, NULL, false)
#20 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), Array, NULL, false)
#21 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), Array, NULL, false)
#22 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#23 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array, NULL)
#24 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#25 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#26 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke('1')
#28 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\UserResource\\Pages\\ViewUser), '__invoke')
#29 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#30 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#31 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#48 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#55 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#59 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#84 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#85 C:\\Users\\<USER>\\Herd\\wms-mpi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#86 C:\\Users\\<USER>\\Herd\\wms-mpi\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#87 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\He...')
#88 {main}
"} 
